package com.kedish.xyhelper_fox.cache;

import com.kedish.xyhelper_fox.component.UserLimitBucketComponent.RateLimit;
import com.kedish.xyhelper_fox.repo.mapper.ChatGptConfigMapper;
import com.kedish.xyhelper_fox.repo.mapper.ForbiddenWordMapper;
import com.kedish.xyhelper_fox.repo.model.ChatGptConfig;
import com.kedish.xyhelper_fox.repo.model.ForbiddenWord;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
@Order(value = Ordered.HIGHEST_PRECEDENCE)
public class LocalCache {

    private final Map<String, String> CONFIG_MAP
            = new ConcurrentHashMap<>(64);

    private final List<String> FORBIDDEN_WORDS = new ArrayList<>();

    // 用户到模型的限速缓存: key = userToken:model, value = RateLimit
    private final Map<String, RateLimit> USER_MODEL_RATE_LIMIT_CACHE = new ConcurrentHashMap<>(256);

    @Resource
    private ChatGptConfigMapper chatGptConfigMapper;

    @Resource
    private ForbiddenWordMapper forbiddenWordMapper;

    @PostConstruct
    public void init() {
        loadForbiddenWords();
        loadConfig();
    }

    public Map<String, String> getConfigMap() {
        return CONFIG_MAP;
    }

    public boolean getBoolean(String config){
        String s = getConfigMap().get(config);
        return Boolean.parseBoolean(s);
    }

    public String getString(String config,String defaultValue){
        String s = getConfigMap().get(config);
        if (StringUtils.hasLength(s)){
            return s;
        }
        return defaultValue;
    }

    public int getNumber(String config){
        String s = getConfigMap().get(config);

        if (StringUtils.hasLength(s)){
            return Integer.parseInt(s);
        }
        return 0;
    }

    public synchronized void setFORBIDDEN_WORDS(List<String> words) {
        FORBIDDEN_WORDS.clear();
        FORBIDDEN_WORDS.addAll(words);
    }

    public synchronized void setCONFIG_MAP(Map<String, String> configMap) {
        CONFIG_MAP.clear();
        CONFIG_MAP.putAll(configMap);
    }

    public List<String> getForbiddenWords() {
        return FORBIDDEN_WORDS;
    }

    @Scheduled(fixedDelay = 5, timeUnit = TimeUnit.MINUTES)
    public synchronized void loadConfig() {

        Map<String, String> allConfigMap = getAllConfigMap();
        setCONFIG_MAP(allConfigMap);
    }

    public Map<String, String> getAllConfigMap() {
        List<ChatGptConfig> list = chatGptConfigMapper.selectList(null);
        return list.stream()
                .filter(e->e.getKey()!=null
                && e.getValue()!=null).collect(Collectors.toMap(ChatGptConfig::getKey, ChatGptConfig::getValue));
    }

    @Scheduled(fixedDelay = 5, timeUnit = TimeUnit.MINUTES)
    public synchronized void loadForbiddenWords() {
        List<String> words = getAll();
        setFORBIDDEN_WORDS(words);
    }

    public List<String> getAll() {
        return forbiddenWordMapper.selectList(null).stream().map(ForbiddenWord::getWord).toList();
    }

    /**
     * 获取用户模型限速缓存
     * @param userToken 用户token
     * @param model 模型名称
     * @return 限速配置，如果不存在则返回null
     */
    public RateLimit getUserModelRateLimit(String userToken, String model) {
        return USER_MODEL_RATE_LIMIT_CACHE.get(userToken + ":" + model.toLowerCase());
    }

    /**
     * 缓存用户模型限速配置
     * @param userToken 用户token
     * @param model 模型名称
     * @param rateLimit 限速配置
     */
    public void cacheUserModelRateLimit(String userToken, String model, RateLimit rateLimit) {
        if (rateLimit != null) {
            USER_MODEL_RATE_LIMIT_CACHE.put(userToken + ":" + model.toLowerCase(), rateLimit);
        }
    }

    /**
     * 清除用户的所有模型限速缓存
     * @param userToken 用户token
     */
    public void clearUserRateLimitCache(String userToken) {
        // 移除所有以userToken开头的缓存项
        USER_MODEL_RATE_LIMIT_CACHE.entrySet().removeIf(entry -> entry.getKey().startsWith(userToken + ":"));
        log.info("Cleared rate limit cache for user: {}", userToken);
    }

    /**
     * 清除特定用户模型的限速缓存
     * @param userToken 用户token
     * @param model 模型名称
     */
    public void clearUserModelRateLimitCache(String userToken, String model) {
        USER_MODEL_RATE_LIMIT_CACHE.remove(userToken + ":" + model.toLowerCase());
        log.info("Cleared rate limit cache for user: {} and model: {}", userToken, model);
    }

    /**
     * 定期清理用户模型限速缓存，防止缓存过大
     */
    @Scheduled(fixedDelay = 30, timeUnit = TimeUnit.MINUTES)
    public synchronized void cleanUserModelRateLimitCache() {
        log.info("Cleaning user model rate limit cache, current size: {}", USER_MODEL_RATE_LIMIT_CACHE.size());
        USER_MODEL_RATE_LIMIT_CACHE.clear();
        log.info("User model rate limit cache cleaned");
    }
}

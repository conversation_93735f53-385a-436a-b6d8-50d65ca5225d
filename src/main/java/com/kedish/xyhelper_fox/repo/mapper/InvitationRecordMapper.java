package com.kedish.xyhelper_fox.repo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kedish.xyhelper_fox.repo.model.InvitationRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface InvitationRecordMapper extends BaseMapper<InvitationRecord> {

    /**
     * 根据邀请人token查询邀请记录
     * @param inviterToken 邀请人token
     * @return 邀请记录列表
     */
    @Select("SELECT * FROM invitation_records WHERE inviter_token = #{inviterToken} ORDER BY invitation_time DESC")
    List<InvitationRecord> selectByInviterToken(@Param("inviterToken") String inviterToken);

    /**
     * 根据被邀请人token查询邀请记录
     * @param inviteeToken 被邀请人token
     * @return 邀请记录
     */
    @Select("SELECT * FROM invitation_records WHERE invitee_token = #{inviteeToken} LIMIT 1")
    InvitationRecord selectByInviteeToken(@Param("inviteeToken") String inviteeToken);

    /**
     * 统计邀请人的邀请数量
     * @param inviterToken 邀请人token
     * @return 邀请数量
     */
    @Select("SELECT COUNT(*) FROM invitation_records WHERE inviter_token = #{inviterToken}")
    int countByInviterToken(@Param("inviterToken") String inviterToken);

    /**
     * 统计邀请人的已激活邀请数量
     * @param inviterToken 邀请人token
     * @return 已激活邀请数量
     */
    @Select("SELECT COUNT(*) FROM invitation_records WHERE inviter_token = #{inviterToken} AND status = 1")
    int countActiveInvitationsByInviterToken(@Param("inviterToken") String inviterToken);

    /**
     * 统计邀请人的已返现邀请数量
     * @param inviterToken 邀请人token
     * @return 已返现邀请数量
     */
    @Select("SELECT COUNT(*) FROM invitation_records WHERE inviter_token = #{inviterToken} AND reward_status = 1")
    int countRewardedInvitationsByInviterToken(@Param("inviterToken") String inviterToken);

    /**
     * 获取邀请人的总返现积分
     * @param inviterToken 邀请人token
     * @return 总返现积分
     */
    @Select("SELECT IFNULL(SUM(reward_points), 0) FROM invitation_records WHERE inviter_token = #{inviterToken} AND reward_status = 1")
    int getTotalRewardPointsByInviterToken(@Param("inviterToken") String inviterToken);
}

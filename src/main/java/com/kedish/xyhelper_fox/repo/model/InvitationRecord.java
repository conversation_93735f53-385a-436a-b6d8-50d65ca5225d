package com.kedish.xyhelper_fox.repo.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("invitation_records")
public class InvitationRecord {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("inviter_name")
    private String inviterName;

    @TableField("invitee_name")
    private String inviteeName;

    @TableField("invitation_time")
    private LocalDateTime invitationTime;

    @TableField("created_at")
    private LocalDateTime createdAt;

    // 返现金额
    @TableField("reward_num")
    private BigDecimal rewardNum;


}

package com.kedish.xyhelper_fox.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.zxing.WriterException;
import com.kedish.xyhelper_fox.cache.LocalCache;
import com.kedish.xyhelper_fox.constant.OrderStatusEnum;
import com.kedish.xyhelper_fox.constant.UserTypeEnum;
import com.kedish.xyhelper_fox.exception.FoxException;
import com.kedish.xyhelper_fox.model.req.PayReq;
import com.kedish.xyhelper_fox.model.resp.PayQrCodeResp;
import com.kedish.xyhelper_fox.pay.epay.EpayService;
import com.kedish.xyhelper_fox.pay.epay.PayRequest;
import com.kedish.xyhelper_fox.pay.epay.PayResponse;
import com.kedish.xyhelper_fox.repo.model.*;
import com.kedish.xyhelper_fox.security.UserContext;
import com.kedish.xyhelper_fox.utils.QrCodeUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;

@Service
@Slf4j
public class PayService {

    @Resource
    private SalesPlanService salesPlanService;

    @Resource
    private CouponService couponService;

    @Resource
    private PaymentMethodService paymentMethodService;
    @Resource
    private EpayService epayService;

    @Resource
    private LocalCache localCache;

    @Resource
    private OrderService orderService;

    @Resource
    private ChatgptUserService chatgptUserService;

    @Resource
    private EmailService emailService;


    @Async
    public void notifyTradeSuccess(Order order) {

        if (order.getStatus().equals(OrderStatusEnum.PAID.getCode())) {
            log.warn("order already paid:{}", JSON.toJSONString(order));
            return;
        }
        log.info("notify trade success:{}", JSON.toJSONString(order));
        orderService.paySuccess(order.getOutTradeNo());

        //增加用户权益
        String buyInfo = order.getBuyInfo();
        JSONObject jsonObject = JSONObject.parseObject(buyInfo);
        Long salesPlanId = jsonObject.getLong("salesPlanId");
        SalesPlan salesPlan = salesPlanService.getById(salesPlanId);

        chatgptUserService.addUserRights(salesPlan.getValidDays(),
                salesPlan.getMembershipType(), order.getUsername());
        //发邮件
        emailService.sendPurchase(order);
    }

    public PayQrCodeResp getQrCodeUrl(PayReq payReq) throws IOException, WriterException {

        if (payReq.getSalesPlanId() == null || payReq.getPaymentMethodId() == null) {
            throw new FoxException("参数错误");
        }
        BigDecimal discount = BigDecimal.ZERO;
        SalesPlan salesPlan = salesPlanService.getById(payReq.getSalesPlanId());
        Coupon coupon = null;
        if (StringUtils.hasLength(payReq.getCouponCode())) {
            coupon = couponService.getCouponByCode(payReq.getCouponCode());
            discount = couponService.checkAccess(coupon, salesPlan);
        }
        BigDecimal amount = salesPlan.getAmount().subtract(discount);

        PaymentMethod paymentMethod
                = paymentMethodService.getById(payReq.getPaymentMethodId());
        if (paymentMethod == null) {
            throw new FoxException("支付方式不存在");
        }
        Map<String, String> configMap = localCache.getConfigMap();
        ChatgptUser user = UserContext.getUser();

        boolean isSuccess = false;
        String outTradeNo = getOutTradeNo(user);
        String base64QrCode = "";
        String tradeNo = "";
        if (paymentMethod.getPaymentType().startsWith("yzf")) {

            PayRequest payRequest = new PayRequest();
            payRequest.setClientip("*************");
            payRequest.setSitename(configMap.get("systemName"));
            payRequest.setMoney(amount.toString());
            payRequest.setOutTradeNo(outTradeNo);
            payRequest.setName(salesPlan.getName());
            payRequest.setMethod("web");
            if (paymentMethod.getPaymentType().equals("yzf_wxpay")) {
                payRequest.setType("wxpay");
            } else if (paymentMethod.getPaymentType().equals("yzf_alipay")) {
                payRequest.setType("alipay");
            }
            payRequest.setReturnUrl(paymentMethod.getCallbackUrl() + "/api/pay/yzf/notify");
            payRequest.setNotifyUrl(paymentMethod.getCallbackUrl() + "/api/pay/yzf/notify");
            log.info("pay request:{}", JSON.toJSONString(payRequest));
            PayResponse pay = epayService.pay(payRequest, paymentMethod);
            if (pay.getCode() == 1) {
                log.info("pay response:{}", JSON.toJSONString(pay));
                //成功
                tradeNo = pay.getTrade_no();
                isSuccess = true;
                base64QrCode = QrCodeUtils.createQRCode(pay.getQrcode());
            } else {
                log.error("pay response:{}", JSON.toJSONString(pay));

                throw new FoxException(pay.getMsg());
            }
        }
        if (isSuccess) {
            orderService.createOrder(tradeNo, outTradeNo,
                    Optional.ofNullable(coupon).map(Coupon::getCouponCode).orElse(""), salesPlan, amount,
                    paymentMethod);
            PayQrCodeResp resp = new PayQrCodeResp();
            resp.setQrCodeBase64(base64QrCode);
            resp.setOutTradeNo(outTradeNo);
            return resp;
        }
        throw new FoxException("不支持的购买方式");
    }

    private String getOutTradeNo(ChatgptUser chatgptUser) {
        return chatgptUser.getUserToken() + "_" + System.currentTimeMillis();
    }
}

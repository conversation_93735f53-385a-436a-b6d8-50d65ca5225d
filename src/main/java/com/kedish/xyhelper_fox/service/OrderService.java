package com.kedish.xyhelper_fox.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kedish.xyhelper_fox.constant.OrderStatusEnum;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.repo.mapper.OrderMapper;
import com.kedish.xyhelper_fox.repo.model.ChatgptUser;
import com.kedish.xyhelper_fox.repo.model.Order;
import com.kedish.xyhelper_fox.repo.model.PaymentMethod;
import com.kedish.xyhelper_fox.repo.model.SalesPlan;
import com.kedish.xyhelper_fox.security.UserContext;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Service
@Slf4j
public class OrderService {

    @Resource
    private OrderMapper orderMapper;

    public void paySuccess(String outTradeNo) {
        Order order = queryOrder(outTradeNo);
        order.setStatus(OrderStatusEnum.PAID.getCode());
        order.setUpdatedAt(LocalDateTime.now());
        orderMapper.updateById(order);
    }


    public void createOrder(String tradeNo, String outTradeNo, String couponCode,
                            SalesPlan salesPlan, BigDecimal amount, PaymentMethod paymentMethod) {
        ChatgptUser user = UserContext.getUser();
        Order order = new Order();
        order.setAmount(amount);
        order.setTradeNo(tradeNo);
        order.setOutTradeNo(outTradeNo);
        order.setUsername(user.getUserToken());
        order.setCreatedAt(LocalDateTime.now());
        order.setStatus(OrderStatusEnum.WAIT_PAY.getCode());
        order.setSalesPlan(salesPlan.getName());
        order.setCouponCode(couponCode);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("salesPlanId", salesPlan.getId());
        jsonObject.put("paymentMethodId", paymentMethod.getId());
//        jsonObject.put("")
        order.setBuyInfo(jsonObject.toString());

        String remark = user.getUserToken() + "购买套餐：" + salesPlan.getName() + "，有效期：" + salesPlan.getValidDays() + "天";
        order.setRemarks(remark);
        orderMapper.insert(order);

    }

    public Order queryOrder(String outTradeNo) {
        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("out_trade_no", outTradeNo);

        return orderMapper.selectOne(queryWrapper);
    }

    public Page<Order> page(PageQueryReq req) {
        Page<Order> page = new Page<>(req.getPageNum(), req.getPageSize());
        // 排序
        if (StringUtils.hasLength(req.getSortField()) && StringUtils.hasLength(req.getSortOrder())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(req.getSortField());
            orderItem.setAsc(req.getSortOrder().equals("asc"));
            page.addOrder(orderItem);

        }
        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
//        queryWrapper.isNull("deleted_at");
        if (StringUtils.hasLength(req.getQuery()))
            queryWrapper.and(wrapper -> wrapper.like("remarks", req.getQuery())
                    .or()
                    .like("username", req.getQuery()));
        return orderMapper.selectPage(
                page, queryWrapper
        );
    }
}

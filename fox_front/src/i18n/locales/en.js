export default {
  // Common
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    search: 'Search',
    reset: 'Reset',
    submit: 'Submit',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    loading: 'Loading...',
    success: 'Operation successful',
    error: 'Operation failed',
    warning: 'Warning',
    info: 'Info',
    close: 'Close',
    refresh: 'Refresh',
    export: 'Export',
    import: 'Import',
    copy: 'Copy',
    paste: 'Paste',
    cut: 'Cut',
    selectAll: 'Select All',
    clear: 'Clear',
    upload: 'Upload',
    download: 'Download',
    preview: 'Preview',
    print: 'Print',
    settings: 'Settings',
    help: 'Help',
    about: 'About',
    version: 'Version',
    language: 'Language',
    theme: 'Theme',
    darkMode: 'Dark Mode',
    lightMode: 'Light Mode',
    goToLogin: 'Go to Login',
    loginRequired: 'Login required to access',
    guestAccess: 'Guest Access',
    chooseAccessMethod: 'Please choose access method',
    authCodeAccess: 'Authorization Code Access',
    authCodeLogin: 'Authorization Code Login',
    enterAuthCode: 'Please enter your authorization code',
    authCodePlaceholder: 'Please enter authorization code',
    authCodeRequired: 'Authorization code cannot be empty',
    authCodeLogging: 'Logging in with authorization code...',
    authCodeSuccess: 'Authorization code login successful!',
    authCodeFailed: 'Authorization code login failed: ',
    guestAccessing: 'Accessing as guest...',
    accessRestricted: 'Access restricted: ',
    accessRestrictedLogin: 'Access restricted, please try logging in',
    carSelectionFailed: 'Car selection failed, please refresh and try again',
    accessFailed: 'Access failed: ',
    tip: 'Tip',
    seconds: ' seconds',
    termsOfService: 'Terms of Service',
    privacyPolicy: 'Privacy Policy'
  },

  // Greetings
  greetings: {
    dawn: 'Good Dawn',
    morning: 'Good Morning',
    noon: 'Good Noon',
    afternoon: 'Good Afternoon',
    evening: 'Good Evening'
  },

  // Navigation Menu
  menu: {
    chatgpt: 'ChatGPT',
    claude: 'Claude',
    grok: 'Grok',
    drawing: '4O Drawing',
    purchase: 'Purchase',
    useNote: 'User Guide',
    announcement: 'Announcements',
    exchange: 'Exchange',
    userCenter: 'User Center',
    myWorks: 'My Works',
    backend: 'Admin Panel',
    logout: 'Logout',
    membership: 'Buy Membership',
    notification: 'Notifications',
    changePassword: 'Change Password',
    theme: 'Switch Theme',
    clickToLogin: 'Click avatar to login'
  },

  // User Related
  user: {
    login: 'Login',
    register: 'Register',
    logout: 'Logout',
    username: 'Username',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    email: 'Email',
    phone: 'Phone',
    captcha: 'Captcha',
    rememberMe: 'Remember Me',
    forgotPassword: 'Forgot Password',
    resetPassword: 'Reset Password',
    changePassword: 'Change Password',
    oldPassword: 'Old Password',
    newPassword: 'New Password',
    profile: 'Profile',
    avatar: 'Avatar',
    nickname: 'Nickname',
    gender: 'Gender',
    birthday: 'Birthday',
    address: 'Address',
    bio: 'Bio',
    loginSuccess: 'Login successful',
    loginFailed: 'Login failed',
    registerSuccess: 'Registration successful',
    registerFailed: 'Registration failed',
    logoutSuccess: 'Logout successful',
    passwordChanged: 'Password changed successfully',
    profileUpdated: 'Profile updated successfully',
    emailVerification: 'Email Verification',
    sendVerificationCode: 'Send Verification Code',
    verificationCodeSent: 'Verification code sent',
    invalidCredentials: 'Invalid username or password',
    accountLocked: 'Account locked',
    accountExpired: 'Account expired',
    permissionDenied: 'Permission denied',
    memberTitle: 'Distinguished Member',
    premiumMemberTitle: 'Distinguished Premium Member'
  },

  // System Configuration
  config: {
    systemName: 'System Name',
    systemLogo: 'System Logo',
    siteNotice: 'Site Notice',
    emailConfig: 'Email Configuration',
    smtpHost: 'SMTP Host',
    smtpPort: 'SMTP Port',
    senderEmail: 'Sender Email',
    emailPassword: 'Email Password',
    emailWhiteList: 'Email Whitelist',
    canRegister: 'Allow Registration',
    registerGift: 'Registration Gift',
    systemMaintenance: 'System Maintenance',
    configUpdated: 'Configuration updated successfully',
    configFailed: 'Configuration update failed'
  },

  // Car Management
  car: {
    carList: 'Car List',
    carName: 'Car Name',
    carStatus: 'Car Status',
    carType: 'Car Type',
    carLimit: 'Usage Limit',
    carExpire: 'Expiration Time',
    addCar: 'Add Car',
    editCar: 'Edit Car',
    deleteCar: 'Delete Car',
    carNotFound: 'Car not found',
    carAccessDenied: 'Car access denied',
    carLimitExceeded: 'Car usage limit exceeded',
    carAdded: 'Car added successfully',
    carUpdated: 'Car updated successfully',
    carDeleted: 'Car deleted successfully'
  },

  // Drawing Function
  drawing: {
    generateImage: 'Generate Image',
    imagePrompt: 'Image Description',
    imageSize: 'Image Size',
    imageCount: 'Generation Count',
    imageStyle: 'Image Style',
    generating: 'Generating...',
    generateSuccess: 'Image generated successfully',
    generateFailed: 'Image generation failed',
    downloadImage: 'Download Image',
    saveImage: 'Save Image',
    shareImage: 'Share Image',
    deleteImage: 'Delete Image',
    imageHistory: 'Generation History',
    noImages: 'No images',
    limitExceeded: 'Drawing limit exceeded'
  },

  // Payment Related
  payment: {
    purchase: 'Purchase',
    price: 'Price',
    discount: 'Discount',
    total: 'Total',
    paymentMethod: 'Payment Method',
    alipay: 'Alipay',
    wechat: 'WeChat Pay',
    bankCard: 'Bank Card',
    paymentSuccess: 'Payment successful',
    paymentFailed: 'Payment failed',
    paymentPending: 'Payment pending',
    refund: 'Refund',
    refundSuccess: 'Refund successful',
    refundFailed: 'Refund failed',
    orderNumber: 'Order Number',
    orderStatus: 'Order Status',
    orderHistory: 'Order History'
  },

  // Activation Code
  activation: {
    activationCode: 'Activation Code',
    exchange: 'Exchange',
    exchangeSuccess: 'Exchange successful',
    exchangeFailed: 'Exchange failed',
    invalidCode: 'Invalid activation code',
    usedCode: 'Activation code already used',
    expiredCode: 'Activation code expired',
    codeHistory: 'Exchange History',
    generateCode: 'Generate Activation Code',
    batchGenerate: 'Batch Generate',
    codeType: 'Code Type',
    codeValue: 'Code Value',
    codeExpire: 'Expiration Time',
    enterCode: 'Enter activation code',
    exchangeNow: 'Exchange Now',
    exchanging: 'Exchanging...',
    tips: {
      title: 'Activation Code Exchange',
      timeLimit: 'Activation codes may have time limits, please use them as soon as possible',
      oneTime: 'Each activation code can only be used once',
      contact: 'If you encounter any issues, please contact customer service'
    }
  },

  // File Upload
  file: {
    upload: 'Upload File',
    uploadSuccess: 'Upload successful',
    uploadFailed: 'Upload failed',
    fileSize: 'File Size',
    fileType: 'File Type',
    fileName: 'File Name',
    fileNotFound: 'File not found',
    fileTooLarge: 'File too large',
    invalidFileType: 'File type not supported',
    selectFile: 'Select File',
    dragToUpload: 'Drag files here to upload',
    uploadProgress: 'Upload Progress'
  },

  // Form Validation
  validation: {
    required: 'This field is required',
    email: 'Please enter a valid email address',
    phone: 'Please enter a valid phone number',
    password: 'Password must be at least 6 characters',
    confirmPassword: 'Passwords do not match',
    minLength: 'Minimum {min} characters required',
    maxLength: 'Maximum {max} characters allowed',
    numeric: 'Please enter a number',
    url: 'Please enter a valid URL',
    date: 'Please enter a valid date',
    time: 'Please enter a valid time'
  },

  // Pagination
  pagination: {
    total: 'Total {total} items',
    page: 'Page {current}',
    pageSize: '{size} items per page',
    goto: 'Go to',
    prev: 'Previous',
    next: 'Next',
    first: 'First',
    last: 'Last'
  },

  // Time Related
  time: {
    now: 'Just now',
    minutesAgo: '{minutes} minutes ago',
    hoursAgo: '{hours} hours ago',
    daysAgo: '{days} days ago',
    weeksAgo: '{weeks} weeks ago',
    monthsAgo: '{months} months ago',
    yearsAgo: '{years} years ago',
    today: 'Today',
    yesterday: 'Yesterday',
    tomorrow: 'Tomorrow',
    thisWeek: 'This week',
    lastWeek: 'Last week',
    thisMonth: 'This month',
    lastMonth: 'Last month',
    thisYear: 'This year',
    lastYear: 'Last year'
  },

  // Status
  status: {
    active: 'Active',
    inactive: 'Inactive',
    enabled: 'Enabled',
    disabled: 'Disabled',
    online: 'Online',
    offline: 'Offline',
    pending: 'Pending',
    processing: 'Processing',
    completed: 'Completed',
    failed: 'Failed',
    cancelled: 'Cancelled',
    expired: 'Expired',
    valid: 'Valid',
    invalid: 'Invalid'
  },

  // Node Types
  nodes: {
    free: 'Free Node',
    plus: 'Plus Node',
    fouro: '4O Node',
    claude: 'Claude Node',
    grok: 'Grok Node',
    recommended: 'Recommended',
    clearTime: 'Clears in {time}',
    neverClear: 'Never Clears',
    teamAvailable: 'Team Available',
    teamNotAvailable: 'Team Not Available',
    noRecommendedNodes: 'No recommended nodes available',
    selectingNode: 'Selecting node...',
    selectionFailed: 'Node selection failed: ',
    fetchError: 'Failed to fetch nodes'
  },

  login: {
    welcome: 'Welcome Back',
    emailOrUsername: 'Email/Username*',
    password: 'Password*',
    loginButton: 'Login',
    noAccount: 'Don\'t have an account?',
    register: 'Sign up',
    or: 'or',
    forgotPassword: 'Forgot password?',
    termsOfService: 'Terms of Service',
    privacyPolicy: 'Privacy Policy',
    loginAnnouncement: 'Login Announcement',
    iKnow: 'I understand',
    goToHome: 'Go to Home',
    errors: {
      enterUsername: 'Please enter your username or email',
      enterPassword: 'Please enter your password',
      usernameError: 'Invalid username',
      passwordError: 'Invalid password',
      usernameTooLong: 'Username is too long',
      passwordTooLong: 'Password is too long'
    }
  },

  register: {
    createAccount: 'Create Account',
    email: 'Email Address*',
    verificationCode: 'Verification Code*',
    username: 'Username*',
    password: 'Password*',
    inviteCode: 'Invitation Code (Optional)',
    registerButton: 'Register',
    hasAccount: 'Already have an account?',
    login: 'Login',
    continue: 'Continue',
    emailSent: 'Verification email sent',
    resend: 'Resend',
    resendCountdown: '{seconds}s until resend',
    termsOfService: 'Terms of Service',
    privacyPolicy: 'Privacy Policy',
    welcome: 'Welcome',
    createAccount: 'Create Account',
    emailPlaceholder: 'Please enter your email address',
    verificationCodePlaceholder: 'Please enter your verification code',
    usernamePlaceholder: 'Please enter your username',
    passwordPlaceholder: 'Please enter your password',
    inviteCodePlaceholder: 'Please enter your invitation code',
    registrationSuccess: 'Registration successful',
    errors: {
      enterEmail: 'Please enter your email address',
      invalidEmail: 'Please enter a valid email address',
      enterUsername: 'Please enter your username',
      enterPassword: 'Please enter your password',
      verificationSent: 'Verification code has been sent, please check your email',
      verificationSuccess: 'Registration successful',
      
    }
  },
  resetPassword: {
    subtitle: 'Reset Password',
    email: 'Email Address*',
    emailPlaceholder: 'Please enter your email address',
    verificationCodePlaceholder: 'Please enter your verification code',
    verificationCode: 'Verification Code*',
    username: 'Username*',
    password: 'Password*',
    inviteCode: 'Invitation Code (Optional)',
    registerButton: 'Register',
    hasAccount: 'Already have an account?',
    login: 'Login',
    continue: 'Continue',
    emailSent: 'Verification email sent',
    resend: 'Resend',
    resendCountdown: '{seconds}s until resend',
    confirmPassword: 'Confirm Password',
    confirmPasswordPlaceholder: 'Please enter your password again',
    resetButton: 'Reset Password',
    rememberPassword: 'Remember password?',
    newPassword: 'New Password',
    newPasswordPlaceholder: 'Please enter your new password',
    errors: {
      enterEmail: 'Please enter your email address',
      invalidEmail: 'Please enter a valid email address',
      enterUsername: 'Please enter your username',
      enterPassword: 'Please enter your password',
      verificationSent: 'Verification code has been sent, please check your email',
      verificationSuccess: 'Registration successful'
    }
  }
}

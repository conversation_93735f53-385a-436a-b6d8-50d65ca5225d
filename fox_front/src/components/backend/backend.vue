<template>
    <div id="common-layout">
        <el-container>
            <!-- 添加一个移动端的顶部导航栏 -->
            <el-header v-if="isMobile" class="mobile-header">
                <el-button @click="toggleAside">
                    <el-icon><Menu /></el-icon>
                </el-button>
                <h2>{{ config.systemName }}</h2>
            </el-header>

            <!-- 修改侧边栏，添加移动端响应式处理 -->
            <el-aside
                :width="isMobile ? '0' : '200px'"
                :class="['aside', { 'mobile-aside': isMobile, 'mobile-aside-open': showAside }]"
            >
                <el-header height="60px" class="header">
                    <chatgptIcon class="icon" style="width: 40px;height: 40px;"/>
                    <h2 @click="goHome" style="cursor: pointer;">{{ config.systemName }}</h2>
                </el-header>

                <div>

                    <el-menu :router="true" :default-active="route.path" class="el-menu-vertical-demo"
                        :collapse="isCollapse">
                        <el-sub-menu index="0">
                            <template #title>
                            <el-icon>
                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                    viewBox="0 0 24 24">
                                    <path
                                        d="M19 19H5V5h9V3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-9h-2v9z"
                                        fill="currentColor"></path>
                                    <path d="M15 13h2v4h-2zm-8-3h2v7H7zm4-3h2v10h-2zm8-2V3h-2v2h-2v2h2v2h2V7h2V5z"
                                        fill="currentColor"></path>
                                </svg>
                            </el-icon>
                            <span>站点统计</span>
                            </template>
                            <el-menu-item index="/dashboard/today">
                                <el-icon>
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                        <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z" fill="currentColor"/>
                                    </svg>
                                </el-icon>
                                <span>今日数据</span>
                            </el-menu-item>
                            <el-menu-item index="/dashboard/user">
                                <el-icon>
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4s-4 1.79-4 4s1.79 4 4 4zm0-6c1.1 0 2 .9 2 2s-.9 2-2 2s-2-.9-2-2s.9-2 2-2zm0 7c-2.67 0-8 1.34-8 4v3h16v-3c0-2.66-5.33-4-8-4zm6 5H6v-.99c.2-.72 3.3-2.01 6-2.01s5.8 1.29 6 2v1z" fill="currentColor"/>
                                    </svg>
                                </el-icon>
                                <span>用户数据</span>
                            </el-menu-item>
                            <el-menu-item index="/dashboard/order">
                                <el-icon>
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14zm-7-2h2V7h-4v2h2z" fill="currentColor"/>
                                        <path d="M7 17h2v-7H7zm8 0h2v-7h-2zm-4 0h2V7h-2z" fill="currentColor"/>
                                    </svg>
                                </el-icon>
                                <span>订单数据</span>
                            </el-menu-item>
                        </el-sub-menu>
                        <el-sub-menu index="1">
                            <template #title>
                                <el-icon>
                                    <User />
                                </el-icon>
                                <span>用户管理</span>
                            </template>
                            <el-menu-item index="/userManage">
                                <el-icon>
                                    <User />
                                </el-icon>
                                <span>用户管理</span>
                            </el-menu-item>
                            <el-menu-item index="/userGroupManage">
                                <el-icon>
                                    <sulvIcon class="icon"/>
                                </el-icon>
                                <span>分组速率</span>
                            </el-menu-item>
                        </el-sub-menu>
                        <el-sub-menu index="2">
                            <template #title>
                                <el-icon>
                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                        viewBox="0 0 20 20">
                                        <g fill="none">
                                            <path
                                                d="M17 5.5A2.5 2.5 0 0 0 14.5 3h-9A2.5 2.5 0 0 0 3 5.5v9A2.5 2.5 0 0 0 5.5 17h9a2.5 2.5 0 0 0 2.5-2.5v-9zm-13 9V13h3v3H5.5l-.144-.007A1.5 1.5 0 0 1 4 14.5zm8-1.5v3H8v-3h4zm2.5 3H13v-3h3v1.5l-.007.145A1.5 1.5 0 0 1 14.5 16zM12 8v4H8V8h4zm1 0h3v4h-3V8zm-1-4v3H8V4h4zm1 0h1.5l.145.007A1.5 1.5 0 0 1 16 5.5V7h-3V4zM7 4v3H4V5.5l.007-.144A1.5 1.5 0 0 1 5.5 4H7zm0 4v4H4V8h3z"
                                                fill="currentColor"></path>
                                        </g>
                                    </svg>
                                </el-icon>
                                <span>账号管理</span>
                            </template>
                            <el-menu-item index="/gptSession">
                                <chatgptIcon class="icon" style="width: 16px !important;height: 16px !important;"/>
                                Chatgpt账号</el-menu-item>
                            <el-menu-item index="/claudeSession">
                                <claudeIcon class="icon" style="width: 16px !important;height: 16px !important;"/>
                                FuClaude账号</el-menu-item>
                            <el-menu-item index="/lyyClaudeSession">
                                <claudeIcon class="icon" style="width: 16px !important;height: 16px !important;"/>
                                LyyClaude账号</el-menu-item>
                            <el-menu-item index="/grokSession">
                                <grokIcon class="icon" style="width: 16px !important;height: 16px !important;"/>
                                Grok账号</el-menu-item>
                        </el-sub-menu>
                        <el-sub-menu index="3">
                            <template #title>
                                <el-icon>
                                    <svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" width="655.359"
                                        height="655.359"
                                        style="shape-rendering:geometricPrecision;text-rendering:geometricPrecision;image-rendering:optimizeQuality;fill-rule:evenodd;clip-rule:evenodd"
                                        viewBox="0 0 6.827 6.827">

                                        <g id="Layer_x0020_1">
                                            <path class="fil0"
                                                d="M3.435 2.033 5.657.866l.05.094-.05-.094a.107.107 0 0 1 .156.096v4.335a.107.107 0 0 1-.163.09L3.435 4.224H1.12a.107.107 0 0 1-.106-.106V2.14c0-.059.047-.107.106-.107h2.315zM5.6 1.136 3.517 2.231a.106.106 0 0 1-.056.016H1.227V4.01h2.232c.017 0 .035.004.051.012l-.05.095.05-.094L5.6 5.12V1.136z" />
                                            <path class="fil0"
                                                d="M1.902 4.01h1.041l-.033.132-.428 1.75-.02.081H1.337l.033-.131.428-1.75.02-.082h.084zm.77.213h-.687L1.608 5.76h.687l.377-1.537zM2.016 2.465h.923v.214h-.923z" />
                                        </g>
                                        <path style="fill:none" d="M0 0h6.827v6.827H0z" />
                                    </svg>
                                </el-icon>
                                <span>营销管理</span>
                            </template>
                            <el-menu-item index="/notice">
                                <el-icon>
                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M12 18.5c.83 0 1.5-.67 1.5-1.5h-3c0 .83.67 1.5 1.5 1.5zM12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8s8 3.59 8 8s-3.59 8-8 8zm4-8.61c0-2.11-1.03-3.92-3-4.39v-.5c0-.57-.43-1-1-1s-1 .43-1 1V7c-1.97.47-3 2.27-3 4.39V14H7v2h10v-2h-1v-2.61zM14 14h-4v-3c0-1.1.9-2 2-2s2 .9 2 2v3z"
                                            fill="currentColor"></path>
                                    </svg>
                                </el-icon>
                                公告设置</el-menu-item>
                        </el-sub-menu>
                        <el-sub-menu index="4">
                            <template #title>
                                <el-icon>
                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                        viewBox="0 0 512 512">
                                        <path
                                            d="M435.25 48h-122.9a14.46 14.46 0 0 0-10.2 4.2L56.45 297.9a28.85 28.85 0 0 0 0 40.7l117 117a28.85 28.85 0 0 0 40.7 0L459.75 210a14.46 14.46 0 0 0 4.2-10.2v-123a28.66 28.66 0 0 0-28.7-28.8z"
                                            fill="none" stroke="currentColor" stroke-linecap="round"
                                            stroke-linejoin="round" stroke-width="32"></path>
                                        <path d="M384 160a32 32 0 1 1 32-32a32 32 0 0 1-32 32z" fill="currentColor">
                                        </path>
                                    </svg>
                                </el-icon>
                                <span>售卖管理</span>
                            </template>
                            <el-menu-item index="/salesPlan">
                                <el-icon>
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">

                                        <g id="sale_1" data-name="sale 1">
                                            <path class="cls-1"
                                                d="M26.4 12 19 5.5a2.73 2.73 0 0 0 0-.5 3 3 0 0 0-6 0 2.73 2.73 0 0 0 .05.5L5.6 12A3.75 3.75 0 0 0 2 15.77v6.46A3.77 3.77 0 0 0 5.77 26h20.46A3.77 3.77 0 0 0 30 22.23v-6.46A3.75 3.75 0 0 0 26.4 12zM16 4a1 1 0 1 1-1 1 1 1 0 0 1 1-1zm12 18.23A1.77 1.77 0 0 1 26.23 24H5.77A1.77 1.77 0 0 1 4 22.23v-6.46A1.77 1.77 0 0 1 5.77 14H6a1 1 0 0 0 .66-.25l7.4-6.48a2.94 2.94 0 0 0 3.88 0l5.4 4.73H11a1 1 0 0 0 0 2h15.23A1.77 1.77 0 0 1 28 15.77z" />
                                            <path class="cls-1"
                                                d="M10.23 18.67a4.53 4.53 0 0 0-1.15-.27l-.5-.09a.55.55 0 0 1-.23-.1.15.15 0 0 1-.07-.12.19.19 0 0 1 .08-.15.62.62 0 0 1 .27-.05.55.55 0 0 1 .24 0A.68.68 0 0 1 9 18a.42.42 0 0 0 .12.08.38.38 0 0 0 .16 0h1.44a.11.11 0 0 0 .1-.05.12.12 0 0 0 .05-.11 1 1 0 0 0-.16-.49 1.76 1.76 0 0 0-.43-.49 2.37 2.37 0 0 0-.71-.36 2.79 2.79 0 0 0-.95-.14 2.91 2.91 0 0 0-1.15.21 1.75 1.75 0 0 0-.77.58 1.42 1.42 0 0 0-.27.86 1.37 1.37 0 0 0 .22.8 1.49 1.49 0 0 0 .64.51 3.4 3.4 0 0 0 1 .27l.56.09a1.06 1.06 0 0 1 .28.1.18.18 0 0 1 .09.15c0 .06-.05.11-.15.14a1.07 1.07 0 0 1-.39.06h-.22a.49.49 0 0 1-.17-.06.42.42 0 0 1-.14-.09L8 20a.4.4 0 0 0-.17 0H6.47a.17.17 0 0 0-.16.16 1.31 1.31 0 0 0 .28.75 1.77 1.77 0 0 0 .77.56 3.37 3.37 0 0 0 1.32.22 3.36 3.36 0 0 0 1.22-.21 2 2 0 0 0 .85-.58 1.38 1.38 0 0 0-.52-2.2zM14.92 16.8a.39.39 0 0 0-.1-.16.27.27 0 0 0-.22-.08H13a.3.3 0 0 0-.23.08.39.39 0 0 0-.1.16l-1.6 4.58a.43.43 0 0 0 0 .05.17.17 0 0 0 .16.16h1.33a.27.27 0 0 0 .2-.06.28.28 0 0 0 .08-.12l.17-.48h1.55l.17.48a.26.26 0 0 0 .28.18h1.33a.17.17 0 0 0 .16-.16.07.07 0 0 0 0-.05zm-1.51 2.73.4-1.33.41 1.33zM20.74 20.11h-2v-3.36a.16.16 0 0 0-.06-.13.18.18 0 0 0-.14-.06h-1.42a.18.18 0 0 0-.14.06.16.16 0 0 0-.06.13v4.64a.21.21 0 0 0 .2.2h3.62a.16.16 0 0 0 .13-.06.18.18 0 0 0 .06-.14v-1.08a.2.2 0 0 0-.06-.14.16.16 0 0 0-.13-.06zM25.36 20.19h-2.22v-.46h2a.17.17 0 0 0 .19-.19v-.93a.22.22 0 0 0 0-.14.2.2 0 0 0-.14-.06h-2V18h2.11a.2.2 0 0 0 .14-.06.21.21 0 0 0 0-.13v-1a.19.19 0 0 0 0-.13.2.2 0 0 0-.14-.06h-3.69a.18.18 0 0 0-.13.06.16.16 0 0 0-.06.13v4.64a.18.18 0 0 0 .06.14.18.18 0 0 0 .13.06h3.75a.18.18 0 0 0 .13-.06.18.18 0 0 0 .06-.14v-1a.2.2 0 0 0-.06-.14.21.21 0 0 0-.13-.12z" />
                                        </g>
                                    </svg>
                                </el-icon>
                                订阅计划</el-menu-item>
                            <el-menu-item index="/conpon">
                                <el-icon>
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
                                        <g id="coupon">
                                            <path class="cls-1"
                                                d="M29 18a1 1 0 0 0 1-1V8.45A1.45 1.45 0 0 0 28.55 7H3.45A1.45 1.45 0 0 0 2 8.45v3.13A1.45 1.45 0 0 0 3.32 13a3 3 0 0 1 0 6A1.45 1.45 0 0 0 2 20.42v3.13A1.45 1.45 0 0 0 3.45 25h25.1A1.45 1.45 0 0 0 30 23.55V21a1 1 0 0 0-2 0v2H14v-.5a1 1 0 0 0-2 0v.5H4v-2.1a5 5 0 0 0 0-9.8V9h8v.5a1 1 0 0 0 2 0V9h14v8a1 1 0 0 0 1 1z" />
                                            <path class="cls-1"
                                                d="M13 16.38a1 1 0 0 0-1 1v2.36a1 1 0 0 0 2 0v-2.36a1 1 0 0 0-1-1zM14 12.26a1 1 0 1 0-2 0v2.36a1 1 0 0 0 2 0zM16.29 20.71a1 1 0 0 0 1.42 0l8-8a1 1 0 0 0-1.42-1.42l-8 8a1 1 0 0 0 0 1.42zM17 13a1 1 0 0 0 0-2 1 1 0 1 0 0 2zM25 21a1 1 0 0 0 0-2 1 1 0 1 0 0 2z" />
                                        </g>
                                    </svg>
                                </el-icon>
                                优惠券</el-menu-item>
                            <el-menu-item index="/activationCode">
                                <el-icon>
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
                                        <path d="M28 8v13h2V8a3.999 3.999 0 0 0-4-4H8.243l2 2H26a1.996 1.996 0 0 1 2 2z"
                                            fill="currentColor">
                                        </path>
                                    </svg>
                                </el-icon>
                                激活码</el-menu-item>
                        </el-sub-menu>
                        <el-sub-menu index="5">
                            <template #title>
                                <el-icon>
                                    <location />
                                </el-icon>
                                <span>支付管理</span>
                            </template>
                            <el-menu-item index="/paymethod">
                                <el-icon>
                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                        viewBox="0 0 16 16">
                                        <g fill="none">
                                            <path
                                                d="M10.5 10a.5.5 0 0 0 0 1h2a.5.5 0 0 0 0-1h-2zM1 5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v6a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V5zm13 0a1 1 0 0 0-1-1H3a1 1 0 0 0-1 1v1h12V5zM2 11a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V7H2v4z"
                                                fill="currentColor"></path>
                                        </g>
                                    </svg>
                                </el-icon>
                                支付渠道</el-menu-item>
                            <el-menu-item index="/order">
                                <el-icon>
                                    <svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" width="655.359"
                                        height="655.359"
                                        style="shape-rendering:geometricPrecision;text-rendering:geometricPrecision;image-rendering:optimizeQuality;fill-rule:evenodd;clip-rule:evenodd"
                                        viewBox="0 0 6.827 6.827">
                                        <g id="Layer_x0020_1">
                                            <g id="_397683080">
                                                <path id="_397683224" class="fil0"
                                                    d="M2.081 1.44h.496v.212H2.08a.048.048 0 0 0-.*************** 0 0 0-.014.034v4.014a.048.048 0 0 0 .048.048h2.664a.048.048 0 0 0 .034-.014.048.048 0 0 0 .014-.034V1.7a.048.048 0 0 0-.014-.034.048.048 0 0 0-.034-.014h-.488V1.44h.488a.258.258 0 0 1 .26.259v4.014a.258.258 0 0 1-.26.26H2.081a.258.258 0 0 1-.259-.26V1.7a.258.258 0 0 1 .26-.26z" />
                                                <path id="_397683896" class="fil0"
                                                    d="M2.682 1.196h1.575v.738H2.577v-.738h.105zm1.364.211H2.787v.316h1.259v-.316z" />
                                                <path id="_397683728" class="fil0"
                                                    d="M3.417.853a.272.272 0 0 1 .193.466.272.272 0 0 1-.466-.193.272.272 0 0 1 .273-.273zm.044.23a.062.062 0 0 0-.107.043.062.062 0 0 0 .107.044.062.062 0 0 0 0-.088z" />
                                                <path id="_397683320" class="fil0" d="M2.465 2.616h1.897v.105H2.465z" />
                                                <path id="_397683656" class="fil0" d="M2.465 3.27h1.897v.106H2.465z" />
                                                <path id="_397682768" class="fil0" d="M2.465 3.926h1.897v.105H2.465z" />
                                                <path id="_397682600" class="fil0" d="M2.465 4.58h1.897v.106H2.465z" />
                                                <path id="_397682696" class="fil0"
                                                    d="M2.225 1.78h2.429v3.741H2.172V1.78h.053zm2.324.106H2.278v3.53h2.271v-3.53z" />
                                            </g>
                                        </g>
                                        <path style="fill:none" d="M0 0h6.827v6.827H0z" />
                                    </svg>
                                </el-icon>
                                订单列表</el-menu-item>
                        </el-sub-menu>
                        <el-sub-menu index="6">
                            <template #title>
                                <el-icon>
                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                        viewBox="0 0 16 16">
                                        <g fill="none">
                                            <path
                                                d="M6 7a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3A.5.5 0 0 1 6 7zm.5 1.5a.5.5 0 0 0 0 1h2a.5.5 0 0 0 0-1h-2zM2 8a6 6 0 1 1 2.996 5.195l-2.338.78a.5.5 0 0 1-.639-.612l.712-2.491A5.975 5.975 0 0 1 2 8zm6-5a5 5 0 0 0-4.3 7.552a.5.5 0 0 1 .05.393l-.509 1.78l1.658-.552a.5.5 0 0 1 .426.052A5 5 0 1 0 8 3z"
                                                fill="currentColor"></path>
                                        </g>
                                    </svg>
                                </el-icon>
                                <span>对话管理</span>
                            </template>
                            <el-menu-item index="/forbiddenWord">
                                <el-icon>
                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                        viewBox="0 0 32 32">
                                        <path d="M28 8v13h2V8a3.999 3.999 0 0 0-4-4H8.243l2 2H26a1.996 1.996 0 0 1 2 2z"
                                            fill="currentColor">
                                        </path>
                                        <path
                                            d="M30 28.586L3.414 2L2 3.414l1.504 1.504A3.918 3.918 0 0 0 2 8v12a4 4 0 0 0 4 4h6v-2H6a1.996 1.996 0 0 1-2-2V8a1.981 1.981 0 0 1 .92-1.667L20.585 22H17l-4 7l1.736 1l3.429-6h4.42l6 6z"
                                            fill="currentColor"></path>
                                    </svg>
                                </el-icon>
                                违禁词</el-menu-item>
                            <el-menu-item index="/conversation">
                                <el-icon>
                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                        viewBox="0 0 512 512">
                                        <path
                                            d="M431 320.6c-1-3.6 1.2-8.6 3.3-12.2a33.68 33.68 0 0 1 2.1-3.1A162 162 0 0 0 464 215c.3-92.2-77.5-167-173.7-167c-83.9 0-153.9 57.1-170.3 132.9a160.7 160.7 0 0 0-3.7 34.2c0 92.3 74.8 169.1 171 169.1c15.3 0 35.9-4.6 47.2-7.7s22.5-7.2 25.4-8.3a26.44 26.44 0 0 1 9.3-1.7a26 26 0 0 1 10.1 2l56.7 20.1a13.52 13.52 0 0 0 3.9 1a8 8 0 0 0 8-8a12.85 12.85 0 0 0-.5-2.7z"
                                            fill="none" stroke="currentColor" stroke-linecap="round"
                                            stroke-miterlimit="10" stroke-width="32">
                                        </path>
                                        <path
                                            d="M66.46 232a146.23 146.23 0 0 0 6.39 152.67c2.31 3.49 3.61 6.19 3.21 8s-11.93 61.87-11.93 61.87a8 8 0 0 0 2.71 7.68A8.17 8.17 0 0 0 72 464a7.26 7.26 0 0 0 2.91-.6l56.21-22a15.7 15.7 0 0 1 12 .2c18.94 7.38 39.88 12 60.83 12A159.21 159.21 0 0 0 284 432.11"
                                            fill="none" stroke="currentColor" stroke-linecap="round"
                                            stroke-miterlimit="10" stroke-width="32">
                                        </path>
                                    </svg>
                                </el-icon>
                                聊天记录</el-menu-item>
                        </el-sub-menu>
                        <el-menu-item index="/drawRecord">
                            <el-icon>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                    <path d="M21.47 4.35l-1.34-.56v9.03l2.43-5.86c.41-1.02-.06-2.19-1.09-2.61m-19.5 3.7L6.93 20a2.01 2.01 0 0 0 1.81 1.26c.26 0 .53-.05.79-.16l7.37-3.05c.75-.31 1.21-1.05 1.23-1.79c.01-.26-.04-.55-.13-.81L13 3.5a1.954 1.954 0 0 0-1.81-1.25c-.26 0-.52.06-.77.15L3.06 5.45a2.016 2.016 0 0 0-1.09 2.6m2.85-1.23l7.51-3.12l4.26 10.25l-7.37 3.05L4.82 6.82z" fill="currentColor"/>
                                </svg>
                            </el-icon>
                            <span>绘图记录</span>
                        </el-menu-item>
                        <el-sub-menu index="7">

                            <template #title>
                                <el-icon>
                                    <setting />
                                </el-icon>
                                <span>系统设置</span>
                            </template>
                            <el-menu-item index="/sysconfig">
                                <el-icon>
                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M21 11h-1.5v-.5h-2v3h2V13H21v1c0 .55-.45 1-1 1h-3c-.55 0-1-.45-1-1v-4c0-.55.45-1 1-1h3c.55 0 1 .45 1 1v1zM8 10v5H6.5v-1.5h-2V15H3v-5c0-.55.45-1 1-1h3c.55 0 1 .45 1 1zm-1.5.5h-2V12h2v-1.5zm7 1.5c.55 0 1 .45 1 1v1c0 .55-.45 1-1 1h-4V9h4c.55 0 1 .45 1 1v1c0 .55-.45 1-1 1zM11 10.5v.75h2v-.75h-2zm2 2.25h-2v.75h2v-.75z"
                                            fill="currentColor"></path>
                                    </svg>
                                </el-icon>
                                基本设置
                            </el-menu-item>
                            <el-menu-item index="/member">
                                <el-icon>
                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                        viewBox="0 0 512 512">
                                        <path
                                            d="M256 8C119.033 8 8 119.033 8 256s111.033 248 248 248s248-111.033 248-248S392.967 8 256 8zm0 448c-110.532 0-200-89.451-200-200c0-110.531 89.451-200 200-200c110.532 0 200 89.451 200 200c0 110.532-89.451 200-200 200zm110.442-81.791c-53.046-96.284-50.25-91.468-53.271-96.085c24.267-13.879 39.482-41.563 39.482-73.176c0-52.503-30.247-85.252-101.498-85.252h-78.667c-6.617 0-12 5.383-12 12V380c0 6.617 5.383 12 12 12h38.568c6.617 0 12-5.383 12-12v-83.663h31.958l47.515 89.303a11.98 11.98 0 0 0 10.593 6.36h42.81c9.14 0 14.914-9.799 10.51-17.791zM256.933 239.906h-33.875v-64.14h27.377c32.417 0 38.929 12.133 38.929 31.709c-.001 20.913-11.518 32.431-32.431 32.431z"
                                            fill="currentColor"></path>
                                    </svg>
                                </el-icon>
                                会员设置
                            </el-menu-item>
                            <el-menu-item index="/thirdLogin">
                                <el-icon>
                                    <svg class="oauth-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                        </svg>
                                </el-icon>
                                三方登录
                            </el-menu-item>
                            <el-menu-item index="/email_config">
                                <el-icon>
                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                        viewBox="0 0 16 16">
                                        <g fill="none">
                                            <path
                                                d="M2 6.038V11a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v1.038zM4 4h8a1 1 0 0 1 1 1v.74L8 8.432L3 5.74V5a1 1 0 0 1 1-1zM3 6.876L7.763 9.44a.5.5 0 0 0 .474 0L13 6.876V11a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V6.876z"
                                                fill="currentColor"></path>
                                        </g>
                                    </svg>
                                </el-icon>
                                邮件设置
                            </el-menu-item>
                            <el-menu-item index="/carSetting">
                                <el-icon>
                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                        viewBox="0 0 640 512">
                                        <path
                                            d="M544 192h-16L419.22 56.02A64.025 64.025 0 0 0 369.24 32H155.33c-26.17 0-49.7 15.93-59.42 40.23L48 194.26C20.44 201.4 0 226.21 0 256v112c0 8.84 7.16 16 16 16h48c0 53.02 42.98 96 96 96s96-42.98 96-96h128c0 53.02 42.98 96 96 96s96-42.98 96-96h48c8.84 0 16-7.16 16-16v-80c0-53.02-42.98-96-96-96zM160 432c-26.47 0-48-21.53-48-48s21.53-48 48-48s48 21.53 48 48s-21.53 48-48 48zm72-240H116.93l38.4-96H232v96zm48 0V96h89.24l76.8 96H280zm200 240c-26.47 0-48-21.53-48-48s21.53-48 48-48s48 21.53 48 48s-21.53 48-48 48z"
                                            fill="currentColor"></path>
                                    </svg>
                                </el-icon>
                                车队设置
                            </el-menu-item>
                            <el-menu-item index="/drawConfig">
                                <el-icon>
                                    <drawSvg style="width: 16px;height: 16px;"/>
                                </el-icon>
                                绘图设置
                            </el-menu-item>
                                <el-menu-item index="/auth">
                                    <el-icon>
                                        <authIcon />
                                    </el-icon>
                                    系统授权
                                </el-menu-item>
                        </el-sub-menu>

                    </el-menu>
                    <!-- <el-radio-group v-model="isCollapse">
                        <el-radio-button :value="false">展开</el-radio-button>
                        <el-radio-button :value="true">关闭</el-radio-button>
                    </el-radio-group> -->
                </div>

            </el-aside>

            <!-- 添加遮罩层 -->
            <div
                v-if="isMobile && showAside"
                class="mobile-mask"
                @click="toggleAside"
            ></div>

            <!-- 修改主内容区域 -->
            <el-main :class="['main-content', { 'mobile-main': isMobile }]">
                <router-view />
            </el-main>
        </el-container>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeMount } from 'vue'
import {
    Document,
    Menu as IconMenu,
    Location,
    Setting,
    Menu,
} from '@element-plus/icons-vue'
import { RouterView } from 'vue-router';
import { useRouter, useRoute } from 'vue-router';
import api from '@/axios'
import claudeIcon from '@/assets/claude-color.svg'
import chatgptIcon from '@/assets/chatgptColorful.svg'
import sulvIcon from '@/assets/sulv.svg'
import drawSvg from '@/assets/draw.svg'
import grokIcon from '@/assets/grokColor.svg'
import authIcon from '@/assets/backend/auth.svg'
const isCollapse = ref(false)
const router = useRouter()
const route = useRoute()
const goHome = () => {
    router.push('/home')
}

// 添加移动端响应式状态
const isMobile = ref(false)
const showAside = ref(false)

// 检查是否为移动端
const checkMobile = () => {
    isMobile.value = window.innerWidth <= 768
}

// 切换侧边栏
const toggleAside = () => {
    showAside.value = !showAside.value
}

onBeforeMount(() => {
    checkMobile()
    window.addEventListener('resize', checkMobile)
})

onMounted(() => {
    if (route.path == '/fox-backend') {
        router.replace('/dashboard/today') // 使用replace更合适，因为这是一个重定向逻辑，不需要保留历史记录
    }
    getConfig()
})

const config = ref({})
const getConfig = async () => {
    const res = await api.post('/api/config/get',
        ["systemName", "systemLogo", "siteNotice", "noteSite", "issuingCardSite", "claudeUrl",
            "nodeFreeName", "node4oName", "nodePlusName", "nodeClaudeName"
        ])
    if (res.status !== 200) {
        ElMessage.error('获取配置失败')
        return
    }
    config.value = res.data.data
    console.log(config.value)
}
</script>

<style scoped>
#common-layout {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.header {
    display: flex;
    align-items: center;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    margin-right: 20px;
}

.nav-menu {
    flex: 1;
}

.aside {
    width: auto;
    padding: 10px;
    background-color: var(--el-bg-color);
    /* 添加背景颜色让圆角效果更明显 */
    border-radius: 10px;
    /* 圆角半径 */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    /* 添加阴影让边框更有立体感 */
    border: 1px solid #e0e0e0;
    /* 添加一个浅色的边框，使得边缘更加清晰 */
    margin: 20px 10px;
}

.main-content {
    padding: 20px;
    margin: 20px;
    background-color: var(--el-bg-color);
    /* 添加背景颜色让圆角效果更明显 */
    border-radius: 15px;
    /* 圆角半径 */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    /* 添加阴影让边框更有立体感 */
    border: 1px solid #e0e0e0;
    /* 添加一个浅色的边框，使得边缘更加清晰 */
}

.el-menu-vertical-demo:not(.el-menu--collapse) {
    width: 200px;
    min-height: 400px;
}

.el-radio-group {
    display: flex;
}

.el-menu {
    border: none
}
.icon {
    width: 1em;
    height: 1em;
    margin-right: 8px;
    vertical-align: middle;
    font-size: 18px;
}

/* 移动端样式 */
.mobile-header {
    display: flex;
    align-items: center;
    padding: 10px;
    background-color: var(--el-bg-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.mobile-aside {
    position: fixed;
    left: -230px;
    top: 0;
    bottom: 0;
    z-index: 1000;
    transition: left 0.3s ease;
    margin: 0;
}

.mobile-aside-open {
    left: 0;
}

.mobile-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

.mobile-main {
    margin: 10px;
    padding: 10px;
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
    .aside {
        margin: 0;
    }

    .main-content {
        margin: 10px;
        padding: 10px;
    }

    .el-menu-vertical-demo:not(.el-menu--collapse) {
        width: 200px;
    }
}
</style>

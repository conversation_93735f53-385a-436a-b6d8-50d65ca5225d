<template>
  <div class="node-grid">
    <div class="cards-container" v-if="cards.length">
      <div class="card-wrapper" v-for="(card, index) in cards" :key="index">
        <div class="custom-card custom-card--cus" :class="card.type + '-card'" @click="handleClick(card)">
          <div class="card-content">
            <!-- Card Header -->
            <div class="card-header">
              <div class="icon-type">
                <claude-color v-if="card.type == 'claude'" />
                <grok-color v-else-if="card.type == 'grok'" />
                <chatgpt v-else />
              </div>
            </div>

            <!-- Card Title and Status -->
            <div class="title-section">
              <div class="card-title">{{ card.title }}</div>
              <div class="free-tag" v-if="card.type === 'free'">free</div>
              <div class="fouro-tag" v-if="card.type === 'fouro'">{{ card.label }}</div>
              <div class="plus-tag" v-if="card.type === 'plus'" :class="{ 'pro-tag': card.label === 'PRO', 'team-tag': card.label === 'TEAM' }">{{
                card.label }}</div>
              <div class="plus-tag" v-if="card.type === 'claude'" :class="{ 'pro-tag': card.label === 'PRO' }">{{
                card.label }}</div>
              <div class="plus-tag" v-if="card.type === 'grok'" :class="{ 'pro-tag': card.label === 'PRO' }">{{
                card.label }}</div>

            </div>

            <!-- Status Indicator -->
            <div class="status-section" :class="getStatusClass(card)">
              <span class="status-text">{{ getStatusText(card) }}</span>
              <div class="progress-indicator">
                <div v-for="(heat, index) in card.heatList" :key="index" class="progress-bar" :class="heat">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-empty :image="empty" v-if="!cards.length" :description="$t('nodes.noRecommendedNodes')"
      style="margin-top: 50px;"> </el-empty>
  </div>
</template>

<script setup>
import { defineProps, watch } from 'vue'
import { ref } from 'vue'
import useUserStore from '@/store/user'
import { ElMessageBox, ElMessage, ElLoading } from 'element-plus';
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import api from '@/axios'
import chatgpt from '@/assets/chatgptColorful.svg'
import claudeColor from '@/assets/claude-color.svg'
import grokColor from '@/assets/grokColor.svg'
import empty from '@/assets/node/empty.png'
import FingerprintJS from '@fingerprintjs/fingerprintjs'
const { t } = useI18n()

// 初始化FingerprintJS
const fpPromise = FingerprintJS.load();

// 获取浏览器指纹
const getVisitorId = async () => {
  try {
    // 先尝试从localStorage获取缓存的指纹
    let fingerprint = localStorage.getItem('visitor_fingerprint');
    if (fingerprint) {
      return fingerprint;
    }

    // 如果没有缓存，则重新生成
    const fp = await fpPromise;
    const result = await fp.get();

    // 使用访客ID作为指纹
    fingerprint = result.visitorId;
    fingerprint = "visitorId_" + fingerprint
    // 缓存指纹到localStorage
    localStorage.setItem('visitor_fingerprint', fingerprint);

    return fingerprint;
  } catch (error) {
    console.error('获取浏览器指纹失败:', error);
    // 如果获取指纹失败，生成一个随机ID作为后备方案
    const fallbackId = 'visitorId_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
    localStorage.setItem('visitor_fingerprint', fallbackId);
    return fallbackId;
  }
}

const router = useRouter()
const userStore = useUserStore()
const user = ref(userStore.user)
const isLoggedIn = userStore.isLoggedIn
const props = defineProps({
  nodes: {
    type: Array,
    required: true
  },
  config: {
    type: Object,
    required: true
  }
})
// const format = (percentage) => ""
const cards = ref([])
watch(() => props.nodes, (newVal, oldVal) => {
  cards.value = []
  for (let item of newVal) {
    cards.value.push({
      type: item.type,
      title: item.carID,
      status: item.clearsIn == undefined || item.clearsIn < 0 ? '推荐' : '将于' + item.clearsIn + '秒后恢复',
      isTeam: !!item.isTeam,
      clearsIn: item.clearsIn,
      heatList: caclHeat(item),
      desc: item.desc,
      label: item.label
    })

  }
  // console.log(cards.value)
})

const caclHeat = (card) => {
  if (!card.count) {
    return ['low', 'low', 'low', 'low']
  }
  let statuses = ['low', 'low', 'low', 'low'];
  const value = Number(card.count);
  if (!value) {
    return statuses;
  }
  if (value > 35) {
    statuses = ['high', 'high', 'high', 'high'];
  } else if (value >= 31) {
    statuses = ['medium', 'high', 'high', 'high'];
  } else if (value >= 26) {
    statuses = ['medium', 'medium', 'high', 'high'];
  } else if (value >= 21) {
    statuses = ['medium', 'medium', 'medium', 'high'];
  } else if (value >= 16) {
    statuses = ['medium', 'medium', 'medium', 'medium'];
  } else if (value >= 11) {
    statuses = ['low', 'medium', 'medium', 'medium'];
  } else if (value >= 6) {
    statuses = ['low', 'low', 'medium', 'medium'];
  } else if (value >= 1) {
    statuses = ['low', 'low', 'low', 'medium'];
  }
  return statuses;
}

const selectClaudeCarId = async (node) => {
  let claudeUrl = props.config.claudeUrl
  let carId = node.title
  let type = node.type
  const req = {
    carId: carId,
    type: type,
  }
  let res = await api.post('/api/chatGpt/car/selectClaudeCar', req)
  if (res.status == 200 && res.data.code == 0) {
    let loginUrl = res.data.data

    window.open(loginUrl)
  } else {
    ElMessage({
      type: 'error',
      message: '选车失败:' + res.data.msg,
    })
  }
}
const selectGrokCarId = async (node) => {
  let grokUrl = props.config.grokUrl
  let carId = node.title
  let type = node.type
  let res = await api.get('/api/chatGpt/car/selectGrokCar?carId=' + carId + '&type=' + type)
  if (res.status == 200 && res.data.code == 0) {
    let loginUrl = res.data.data
    window.open(loginUrl)
  } else {
    ElMessage({
      type: 'error',
      message: '选车失败:' + res.data.msg,
    })
  }
}
const selectCarId = async (node) => {
  let carId = node.title
  let type = node.type
  if (type == 'fouro') {
    type = '4o'
  }
  const req = {
    carId: carId,
    type: type,
  }
  const res = await api.post('/api/chatGpt/car/selectCar', req)
  if (res.data.code == 0) {
    let carId = res.data.data
    if (carId) {
      let config = {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
      }
      const formData = new FormData()
      formData.append('usertoken', user.value.userToken || '')
      formData.append('action', 'default')
      const res = await api.post('/auth/login?carid=' + carId, formData, config)
      if (res.status == 200) {
        window.location.href = '/'
      } else {
        console.log(res)
        ElMessage({
          type: 'error',
          message: '登录失败,登录状态码:' + res.status
        })
      }

    } else {
      ElMessage({
        type: 'error',
        message: '选车失败，请刷新重试',
      })
    }
  } else {
    ElMessage({
      type: 'error',
      message: '选车失败:' + res.data.msg,
    })
  }
}
const handleClick = async (node) => {
  // console.log(`Clicked node ${node.title}`)
  if (!isLoggedIn) {
    // 显示访问方式选择对话框
    showAccessMethodDialog(node)
  } else {
    let loadingInstance = ElLoading.service({
      lock: true,
      text: t('nodes.selectingNode'),
      background: 'rgba(0, 0, 0, 0.7)'
    })
    try {
      if (node.type == 'claude') {
        await selectClaudeCarId(node)
      } else if (node.type == 'grok') {
        await selectGrokCarId(node)
      } else {
        await selectCarId(node)
      }
    } catch (e) {
      ElMessage.error(t('nodes.selectionFailed') + e.message)
    } finally {
      loadingInstance.close()
      console.log('closed loading')
    }
  }
}

// 显示访问方式选择对话框
const showAccessMethodDialog = (node) => {
  const enableVisitor = props.config.enableVisitor === 'true'
  const enableUserTokenLogin = props.config.enableUserTokenLogin === 'true'
  
  // 构建访问方式选项
  const options = [
    { label: t('common.goToLogin'), value: 'login' }
  ]
  
  if (enableVisitor) {
    options.push({ label: t('common.guestAccess'), value: 'guest' })
  }
  
  if (enableUserTokenLogin) {
    options.push({ label: t('common.authCodeAccess'), value: 'userToken' })
  }
  
  // 如果只有一个选项，直接执行
  if (options.length === 1) {
    router.push("/login")
    return
  }
  
  // 创建选项按钮HTML
  const optionsHtml = options.map(option => 
    `<button class="access-method-btn" data-value="${option.value}">${option.label}</button>`
  ).join('')
  
  ElMessageBox({
    title: t('common.tip'),
    message: `
      <div style="text-align: center; padding: 10px;">
        <p style="margin-bottom: 15px;">${t('common.loginRequired')}</p>
        <div class="access-methods" style="display: flex; flex-direction: column; gap: 8px;">
          ${optionsHtml}
        </div>
      </div>
    `,
    dangerouslyUseHTMLString: true,
    showConfirmButton: false,
    showCancelButton: false,
    customClass: 'access-method-dialog',
    modalClass: 'access-method-modal',
    beforeClose: (action, instance, done) => {
      done()
    }
  }).then(() => {
    // 处理选择结果在按钮点击事件中
  }).catch(() => {
    // 用户关闭了对话框
  })
  
  // 添加按钮点击事件监听
  setTimeout(() => {
    const buttons = document.querySelectorAll('.access-method-btn')
    buttons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const value = e.target.getAttribute('data-value')
        handleAccessMethodSelection(value, node)
        // 关闭对话框
        const dialog = document.querySelector('.access-method-dialog')
        if (dialog) {
          const closeBtn = dialog.querySelector('.el-message-box__headerbtn')
          if (closeBtn) closeBtn.click()
        }
      })
    })
  }, 100)
}

// 处理访问方式选择
const handleAccessMethodSelection = (method, node) => {
  switch (method) {
    case 'login':
      router.push("/login")
      break
    case 'guest':
      handleGuestAccess(node)
      break
    case 'userToken':
      showUserTokenDialog(node)
      break
  }
}

// 显示授权码输入对话框
const showUserTokenDialog = (node) => {
  ElMessageBox.prompt(t('common.enterAuthCode'), t('common.authCodeLogin'), {
    confirmButtonText: t('common.confirm'),
    cancelButtonText: t('common.cancel'),
    inputPlaceholder: t('common.authCodePlaceholder'),
    inputValidator: (value) => {
      if (!value || value.trim() === '') {
        return t('common.authCodeRequired')
      }
      return true
    }
  }).then(async ({ value }) => {
    await handleUserTokenLogin(value.trim(), node)
  }).catch(() => {
    // 用户取消了输入
  })
}

// 处理授权码登录
const handleUserTokenLogin = async (userToken, node) => {
  let loadingInstance = ElLoading.service({
    lock: true,
    text: t('common.authCodeLogging'),
    background: 'rgba(0, 0, 0, 0.7)'
  })
  
  try {
    const success = await userStore.loginByUserToken(userToken)
    if (success) {
      // 更新组件的用户信息
      user.value = userStore.user
      ElMessage.success(t('common.authCodeSuccess'))
      // 登录成功后继续选车
      await continueWithCarSelection(node)
    }
  } catch (error) {
    ElMessage.error(t('common.authCodeFailed') + error.message)
  } finally {
    loadingInstance.close()
  }
}

// 继续选车流程
const continueWithCarSelection = async (node) => {
  let loadingInstance = ElLoading.service({
    lock: true,
    text: t('nodes.selectingNode'),
    background: 'rgba(0, 0, 0, 0.7)'
  })
  
  try {
    if (node.type == 'claude') {
      await selectClaudeCarId(node)
    } else if (node.type == 'grok') {
      await selectGrokCarId(node)
    } else {
      await selectCarId(node)
    }
  } catch (e) {
    ElMessage.error(t('nodes.selectionFailed') + e.message)
  } finally {
    loadingInstance.close()
  }
}

// 修改游客访问处理函数
const handleGuestAccess = async (node) => {
  let loadingInstance = ElLoading.service({
    lock: true,
    text: t('common.guestAccessing'),
    background: 'rgba(0, 0, 0, 0.7)'
  })

  try {
    const visitorId = await getVisitorId();

    if (node.type == 'claude') {
      // Claude游客访问逻辑
      let claudeUrl = props.config.claudeUrl
      let carId = node.title
      let type = node.type
      let res = await api.get(`/api/chatGpt/car/selectClaudeCar?carId=${carId}&type=${type}&visitorId=${visitorId}`)
      if (res.status == 200 && res.data.code == 0) {
        let loginUrl = res.data.data
        window.open(claudeUrl + loginUrl)
      } else {
        ElMessage({
          type: 'warning',
          message: t('common.accessRestricted') + res.data.msg,
        })
      }
    } else {
      // 其他类型访问逻辑
      let carId = node.title
      let type = node.type
      if (type == 'fouro') {
        type = '4o'
      }
      const req = {
        carId: carId,
        type: type,
      }
      const res = await api.post('/api/chatGpt/car/selectCar?visitorId=' + visitorId, req)
      if (res.data.code == 0) {
        let selectedCarId = res.data.data
        if (selectedCarId) {
          const formData = new FormData()
          formData.append('usertoken', visitorId)
          formData.append('action', 'default')
          const res = await api.post('/auth/login?carid=' + selectedCarId, formData)
          if (res.status == 200) {
            window.location.href = '/'
          } else {
            ElMessage({
              type: 'warning',
              message: t('common.accessRestrictedLogin'),
            })
          }
        } else {
          ElMessage({
            type: 'warning',
            message: t('common.carSelectionFailed'),
          })
        }
      } else {
        ElMessage({
          type: 'warning',
          message: t('common.accessRestricted') + res.data.msg,
        })
      }
    }
  } catch (e) {
    ElMessage.error(t('common.accessFailed') + e.message)
  } finally {
    loadingInstance.close()
  }
}

// Add these new helper functions
const getStatusClass = (card) => {
  const isHigh = card.heatList.every(heat => heat === 'high');
  return isHigh ? 'status-busy' : 'status-normal';
}

const getStatusText = (card) => {
  if (card.clearsIn === undefined || card.clearsIn < 0) {
    return card.desc || t('nodes.recommended')
  }
  return t('nodes.clearTime', { time: card.clearsIn + t('common.seconds') })
}
</script>

<style>
.node-grid {
  margin-top: 20px;
  overflow-y: auto;
  height: 100%;
  overflow-x: hidden;
  --free-color: rgb(36, 212, 174);
  --car-busy-color: #f7b05b;
  --car-very-busy-color: #f56c6c;
}

.node-grid .cards-container {
  display: flex;
  align-items: flex-start;
  align-content: flex-start;
  gap: 24px;
  align-self: stretch;
  flex-wrap: wrap;
  /* padding: 0 12px; */
  justify-content: flex-start;
  /* 确保卡片从左边开始排列 */
}

.node-grid .card-wrapper {
  flex: 0 0 230px;
  /* 改为固定宽度，不伸缩 */
  width: 230px;
  min-width: 230px;
  max-width: 230px;
}

.custom-card {
  background: rgba(0, 0, 0, 0.04);
  border-radius: 10px;
  margin-bottom: 20px;
  cursor: pointer;
  display: flex;
  height: 160px;
  padding: 24px;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  /* 确保卡片填充wrapper */
  box-sizing: border-box;
  transition: transform 0.2s ease;
  border: 2px solid transparent;
  /* 添加透明边框 */
}

.custom-card:hover {
  border-color: #5AC4FD;
  /* 只改变边框颜色 */
  box-shadow: 0 2px 12px 0 rgba(90, 196, 253, 0.1);
}

.card-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.icon-type {
  width: 21px;
  height: 21px;
}

.card-tags {
  display: flex;
  gap: 8px;
}

.team-tag,
.type-tag {
  border: none;
  padding: 2px 8px;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 10px 0;
}

.card-title {
  font-size: 16px;
  font-family: MiSans, sans-serif;
  font-weight: 380;
  color: black;
}

.free-tag,
.fouro-tag {
  font-size: 12px;
  font-family: MiSans, sans-serif;
  font-weight: 520;
}

.plus-tag {
  color: #FFF;
  font-family: MiSans;
  font-size: 10px;
  font-style: normal;
  font-weight: 520;
  line-height: 118%;
  padding: 2px 6px;
  border-radius: 36px;
  background: #0D77FF;
}

.pro-tag {
  border-radius: 33px;
  background: linear-gradient(270deg, #2B88FF 0%, #7057FF 50.48%, #C93FFF 100%), linear-gradient(270deg, #519DFE 0%, #E195FF 100%);
}

.team-tag {
  border-radius: 33px;
  background: linear-gradient(270deg, #FF6B6B 0%, #FF8E53 50.48%, #FFA63D 100%);
  box-shadow: 0 2px 4px rgba(255, 107, 107, 0.2);
}

.free-tag {
  color: #5898EA;
}

.fouro-tag {
  color: #FF9800;
}

.plus-tag {
  color: #FFFFFF;
}

.status-section {
  background: white;
  border-radius: 20px;
  padding: 4px 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
  gap: 8px;
  width: fit-content;
}

.status-text {
  font-size: 13px;
  font-family: MiSans, sans-serif;
  font-weight: 520;
}

.status-busy .status-text {
  color: #FC5E5E;
}

.progress-indicator {
  display: flex;
  gap: 1px;
  width: 48px;
  height: 4px;
}

.progress-bar {
  flex: 1;
  height: 4px;
  border-radius: 30px;
  background-color: var(--free-color);
}

.progress-bar.medium {
  background-color: var(--car-busy-color);
}

.progress-bar.high {
  background-color: var(--car-very-busy-color);
}


.claude-tag-2 {
  background-color: var(--claude-color);
}

.grok-tag-2 {
  background-color: var(--grok-color);
}

.free-tag-2 {
  background-color: var(--free-color);
}

.fouro-tag-2 {
  background-color: var(--fouro-color);
}

.plus-tag-2 {
  background-color: var(--plus-color);
}

/* 响应式布局调整 */
@media screen and (max-width: 1920px) {
  .node-grid .card-wrapper {
    max-width: 230px;
    /* 4列布局 */
  }
}

@media screen and (max-width: 1600px) {
  .node-grid .card-wrapper {
    max-width: calc(33.333% - 16px);
    /* 3列布局 */
  }
}

@media screen and (max-width: 1200px) {
  .node-grid .card-wrapper {
    max-width: calc(50% - 12px);
  }
}

@media screen and (max-width: 768px) {
  .node-grid .card-wrapper {
    max-width: calc(50% - 12px);
    min-width: calc(50% - 12px);
  }

  .node-grid .cards-container {
    gap: 16px;
    padding: 0 8px;
  }

  .custom-card {
    padding: 16px;
    height: 140px;
  }

  .card-title {
    font-size: 14px;
  }

  .status-text {
    font-size: 12px;
  }
}

/* 访问方式选择对话框样式 */
:global(.access-method-dialog) {
  border-radius: 12px !important;
  overflow: hidden;
  backdrop-filter: blur(10px);
  width: 300px !important; /* 设置较小的宽度 */
  max-width: 90vw;
}

:global(.access-method-modal) {
  background-color: transparent !important; /* 移除遮罩层的暗色背景 */
}

:global(.access-method-dialog.el-message-box) {
  border: 1px solid rgba(90, 196, 253, 0.2);
  padding: 15px 20px 12px;
  background: rgba(255, 255, 255, 0.98);
}

:global(.access-method-dialog .el-message-box__title) {
  font-family: MiSans, sans-serif;
  font-weight: 520;
  font-size: 16px;
  color: #333;
}

:global(.access-method-dialog .el-message-box__content) {
  padding: 15px !important;
  background: transparent;
}
:global(.access-method-dialog .el-message-box__message) {
  margin: auto;
}

:global(.access-method-dialog .el-message-box__message p) {
  font-family: MiSans, sans-serif;
  font-weight: 380;
  font-size: 14px;
  color: #555;
  margin-bottom: 15px;
  text-align: center;
}

:global(.access-method-btn) {
  width: 100%;
  padding: 12px 20px;
  border: 1px solid transparent;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.04);
  color: #333;
  font-family: MiSans, sans-serif;
  font-size: 14px;
  font-weight: 520;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0;
  position: relative;
  overflow: hidden;
}

:global(.access-method-btn::before) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(90, 196, 253, 0.1) 0%, rgba(113, 87, 255, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}


:global(.access-method-btn:hover::before) {
  opacity: 1;
}

:global(.access-method-btn:active) {
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(90, 196, 253, 0.2);
}

/* 特殊按钮样式 - 登录按钮和游客访问按钮 */
:global(.access-method-btn[data-value="login"]),
:global(.access-method-btn[data-value="guest"]) {
  background: #5AC4FD;
  color: white;
}


/* 授权码按钮特殊样式 */
:global(.access-method-btn[data-value="userToken"]) {
  background: #5AC4FD;
  color: white;
}

:global(.access-method-btn[data-value="userToken"]:hover) {
  background: #5AC4FD;
  color: white;
}

</style>